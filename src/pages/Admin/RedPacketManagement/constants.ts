/**
 * 红包状态选项，用于UI显示
 */
export const STATUS_OPTIONS = [
  { label: '活跃', value: 'active', color: 'success' },
  { label: '已过期', value: 'expired', color: 'warning' },
  { label: '已领完', value: 'empty', color: 'default' },
  { label: '已取消', value: 'cancelled', color: 'error' },
];

/**
 * 红包类型选项，用于UI显示
 */
export const TYPE_OPTIONS = [
  { label: '随机金额', value: 'random', color: 'blue' },
  { label: '固定金额', value: 'fixed', color: 'purple' },
];

/**
 * ProTable状态枚举
 */
export const STATUS_ENUM = {
  active: { text: '可领取', status: 'Success' },
  expired: { text: '已过期', status: 'Warning' },
  empty: { text: '已领完', status: 'Default' },
  cancelled: { text: '已取消', status: 'Error' },
};

/**
 * ProTable类型枚举
 */
export const TYPE_ENUM = {
  random: { text: '随机金额', status: 'Processing' },
  fixed: { text: '固定金额', status: 'Default' },
};

/**
 * 领取记录表格分页设置
 */
export const CLAIMS_PAGINATION = {
  defaultPageSize: 5,
  showSizeChanger: false,
  pageSizeOptions: ['5', '10', '20'],
};

/**
 * 红包类型选项 (private/group)
 */
export const RED_PACKET_TYPE_OPTIONS = [
  { label: '私聊红包', value: 'private', color: 'blue' },
  { label: '群组红包', value: 'group', color: 'green' },
];

/**
 * 抽奖状态选项
 */
export const DRAW_STATUS_OPTIONS = [
  { label: '等待开奖', value: 'pending', color: 'orange' },
  { label: '已开奖', value: 'drawn', color: 'green' },
  { label: '已取消', value: 'cancelled', color: 'red' },
];

/**
 * 支付状态选项
 */
export const PAY_STATUS_OPTIONS = [
  { label: '未支付', value: 0, color: 'red' },
  { label: '已支付', value: 1, color: 'green' },
];

/**
 * 是否选项 (通用)
 */
export const YES_NO_OPTIONS = [
  { label: '否', value: 0, color: 'default' },
  { label: '是', value: 1, color: 'blue' },
];

/**
 * 表格搜索配置
 */
export const SEARCH_CONFIG = {
  labelWidth: 120,
  columns: 3, // 设置搜索表单为3列布局
};
