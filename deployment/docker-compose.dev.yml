version: '3.8'

services:
  admin-web:
    build:
      context: ..
      dockerfile: deployment/Dockerfile.dev
    ports:
      - '81:1080'   # Map development port
    volumes:
      # Mount the entire project directory to /app
      - ..:/app
      # Use named volume for node_modules to avoid conflicts
      - node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=1080
      - UMI_ENV=development
    container_name: xpay-admin-web-dev
    restart: unless-stopped
    # Enable hot reload by mounting source code
    stdin_open: true
    tty: true
    networks:
      - xpay_dev-network

volumes:
  node_modules:

networks:
  xpay_dev-network:
    driver: bridge
