import { useUnifiedFields } from '@/components/UnifiedFields';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, Select, Space, Tag, Tooltip } from 'antd';
import {
  DRAW_STATUS_OPTIONS,
  PAY_STATUS_OPTIONS,
  RED_PACKET_TYPE_OPTIONS,
  STATUS_ENUM,
  STATUS_OPTIONS,
  TYPE_ENUM,
  TYPE_OPTIONS,
  YES_NO_OPTIONS,
} from '../constants';
import type { RedPacketItem } from '../types';

/**
 * 获取状态标签的辅助函数
 */
const getStatusTag = (status?: string) => {
  const option = STATUS_OPTIONS.find((item) => item.value === status);
  if (!option) {
    return <Tag>未知</Tag>;
  }
  return <Tag color={option.color}>{option.label}</Tag>;
};

/**
 * 获取类型标签的辅助函数
 */
const getTypeTag = (type?: string) => {
  const option = TYPE_OPTIONS.find((item) => item.value === type);
  if (!option) {
    return <Tag>未知</Tag>;
  }
  return <Tag color={option.color}>{option.label}</Tag>;
};

/**
 * 获取红包类型标签的辅助函数
 */
const getRedPacketTypeTag = (redPacketType?: string) => {
  const option = RED_PACKET_TYPE_OPTIONS.find((item) => item.value === redPacketType);
  if (!option) {
    return redPacketType || '--';
  }
  return <Tag color={option.color}>{option.label}</Tag>;
};

/**
 * 获取抽奖状态标签的辅助函数
 */
const getDrawStatusTag = (drawStatus?: string) => {
  if (!drawStatus) return '--';
  const option = DRAW_STATUS_OPTIONS.find((item) => item.value === drawStatus);
  if (!option) {
    return drawStatus;
  }
  return <Tag color={option.color}>{option.label}</Tag>;
};

/**
 * 获取支付状态标签的辅助函数
 */
const getPayStatusTag = (isPay?: number) => {
  const option = PAY_STATUS_OPTIONS.find((item) => item.value === isPay);
  if (!option) {
    return '--';
  }
  return <Tag color={option.color}>{option.label}</Tag>;
};

/**
 * 获取是否标签的辅助函数
 */
const getYesNoTag = (value?: number) => {
  const option = YES_NO_OPTIONS.find((item) => item.value === value);
  if (!option) {
    return '--';
  }
  return <Tag color={option.color}>{option.label}</Tag>;
};

/**
 * 获取基础红包表格列（不包含统一搜索字段）
 * @param onViewDetail - 查看红包详情的回调
 * @param onCancel - 取消红包的回调
 * @param onViewClaims - 查看领取记录的回调
 * @param symbols - 代币符号列表
 * @param tokenLoading - 代币符号列表加载状态
 * @returns ProColumns配置
 */
export const getBaseRedPacketColumns = (
  onViewDetail: (redPacketId: number) => void,
  onCancel: (redPacketId: number) => void,
  onViewClaims: (redPacketId: number, tokenSymbol: string) => void,
  symbols: string[] = [],
  tokenLoading: boolean = false,
): ProColumns<RedPacketItem>[] => {
  return [
    {
      title: 'ID',
      dataIndex: 'redPacketId',
      key: 'redPacketId',
      width: 80,
      search: false,
    },
    {
      title: '红包UUID',
      dataIndex: 'uuid',
      key: 'uuid',
      width: 200,
      ellipsis: true,
      copyable: true,
      fieldProps: {
        placeholder: '请输入红包UUID',
      },
      render: (text) => text || '--',
    },
    // {
    //   title: '创建者账户',
    //   dataIndex: 'creatorUserId',
    //   key: 'creatorUserId',
    //   ellipsis: true,
    //   render: (text) => text || '--',
    // },
    // {
    //   title: '创建者',
    //   dataIndex: 'creatorUsername',
    //   key: 'creatorUsername',
    //   ellipsis: true,
    //   render: (text) => text || '--',
    // },
    // {
    //   title: '发送者账户',
    //   dataIndex: 'senderUserId',
    //   key: 'senderUserId',
    //   ellipsis: true,
    //   render: (text) => text || '--',
    // },
    // {
    //   title: '发送者',
    //   dataIndex: 'senderUsername',
    //   key: 'senderUsername',
    //   ellipsis: true,
    //   render: (text) => text || '--',
    // },
    {
      title: '代币',
      dataIndex: 'symbol',
      key: 'symbol',
      hideInTable: false,
      formItemProps: {
        label: '代币',
      },
      renderFormItem: () => (
        <Select
          placeholder="选择代币"
          loading={tokenLoading}
          allowClear
          showSearch
          optionFilterProp="children"
        >
          {symbols.map((symbol: string) => (
            <Select.Option key={symbol} value={symbol}>
              {symbol}
            </Select.Option>
          ))}
        </Select>
      ),
      render: (_, record) => (
        <Space>
          {record.tokenLogo && (
            <img
              src={record.tokenLogo}
              alt={record.symbol}
              style={{ width: 20, height: 20 }}
            />
          )}
          {record.symbol}
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      valueEnum: TYPE_ENUM,
      render: (_, record) => getTypeTag(record.type),
      search: false,
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      search: false,
    },
    {
      title: '总数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      search: false,
    },
    {
      title: '剩余金额',
      dataIndex: 'remainingAmount',
      key: 'remainingAmount',
      search: false,
    },
    {
      title: '剩余数量',
      dataIndex: 'remainingQuantity',
      key: 'remainingQuantity',
      width: 80,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: STATUS_ENUM,
      render: (_, record) => getStatusTag(record.status),
      valueType: 'select',
      fieldProps: {
        options: STATUS_OPTIONS.map((item) => ({
          label: item.label,
          value: item.value,
        })),
      },
    },

    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      //valueType: 'dateTime',
      hideInSearch: true,
      defaultSortOrder: 'descend',
    },
    {
      title: '过期时间',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      //valueType: 'dateTime',
      search: false,
    },
    {
      title: '红包类型',
      dataIndex: 'redPacketType',
      key: 'redPacketType',
      width: 100,
      render: (_, record) => getRedPacketTypeTag(record.redPacketType),
      search: false,
    },
    {
      title: '留言',
      dataIndex: 'memo',
      key: 'memo',
      width: 150,
      ellipsis: true,
      render: (text) => {
        if (!text) return '--';
        return (
          <Tooltip title={text}>
            <span>{text}</span>
          </Tooltip>
        );
      },
      search: false,
    },
    {
      title: '抽奖状态',
      dataIndex: 'drawStatus',
      key: 'drawStatus',
      width: 100,
      render: (_, record) => getDrawStatusTag(record.drawStatus),
      search: false,
    },
    {
      title: '群组名称',
      dataIndex: 'groupTitle',
      key: 'groupTitle',
      width: 150,
      ellipsis: true,
      render: (text) => text || '--',
      search: false,
    },
    {
      title: '支付状态',
      dataIndex: 'isPay',
      key: 'isPay',
      width: 100,
      render: (_, record) => getPayStatusTag(record.isPay),
      search: false,
    },
    {
      title: '流水要求',
      dataIndex: 'bettingVolume',
      key: 'bettingVolume',
      width: 120,
      render: (_, record) => {
        if (!record.bettingVolume || record.bettingVolume === '0') return '--';
        return `${record.bettingVolume} (${record.bettingVolumeDays || 0}天)`;
      },
      search: false,
    },
    {
      title: '会员要求',
      dataIndex: 'isPremium',
      key: 'isPremium',
      width: 100,
      render: (_, record) => getYesNoTag(record.isPremium),
      search: false,
    },
    // {
    //   title: '操作',
    //   key: 'action',
    //   valueType: 'option',
    //   width: 200,
    //   render: (_, record) => [
    //     <Button
    //       type="link"
    //       key="detail"
    //       onClick={() => record.redPacketId && onViewDetail(record.redPacketId)}
    //     >
    //       详情
    //     </Button>,
    //     <Button
    //       type="link"
    //       key="claims"
    //       onClick={() =>
    //         record.redPacketId &&
    //         onViewClaims(record.redPacketId, record.symbol || '')
    //       }
    //     >
    //       领取记录
    //     </Button>,
        // 隐藏取消按钮
        // record.status === 'active' && (
        //   <a
        //     key="cancel"
        //     style={{ color: 'red' }}
        //     onClick={() => record.redPacketId && onCancel(record.redPacketId)}
        //   >
        //     取消
        //   </a>
        // ),
    //   ],
    // },
  ];
};

/**
 * 获取带有统一搜索字段的完整红包表格列
 */
export const getRedPacketColumns = (
  onViewDetail: (redPacketId: number) => void,
  onCancel: (redPacketId: number) => void,
  onViewClaims: (redPacketId: number, tokenSymbol: string) => void,
  symbols: string[] = [],
  tokenLoading: boolean = false,
): ProColumns<RedPacketItem>[] => {
  const baseColumns = getBaseRedPacketColumns(
    onViewDetail,
    onCancel,
    onViewClaims,
    symbols,
    tokenLoading,
  );

  return useUnifiedFields(
    baseColumns,
    {
      dateRange: { search: true, display: false },
      firstAgent: { search: true, display: true },
      secondAgent: { search: true, display: true },
      thirdAgent: { search: true, display: true },
      telegramId: { search: true, display: true },
      telegramUsername: { search: true, display: true },
      firstName: { search: true, display: true },
    },
    undefined,
    2, // 在ID、UUID之后插入统一字段
  );
};
